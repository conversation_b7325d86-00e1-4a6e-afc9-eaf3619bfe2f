package com.hellofresh.cif.api.ingredientdepletion

import com.hellofresh.cif.api.calculation.generated.model.IngredientDepletionResponse
import com.hellofresh.cif.api.calculation.generated.model.IngredientSummary
import com.hellofresh.cif.api.error.mapToErrorResponse
import com.hellofresh.cif.api.ktor.configureSerialization
import io.ktor.http.HttpStatusCode
import io.ktor.server.application.Application
import io.ktor.server.application.ApplicationCall
import io.ktor.server.auth.authenticate
import io.ktor.server.response.respond
import io.ktor.server.routing.Routing
import io.ktor.server.routing.get
import io.ktor.server.routing.route
import io.ktor.server.routing.routing
import kotlin.time.Duration
import kotlinx.coroutines.withTimeout

fun Routing.ingredientDepletion(
    ingredientDepletionService: IngredientDepletionService,
    timeoutInMillis: Long,
) =
    authenticate {
        route("/api/v1/ingredient-depletion") {
            get {
                handleGetIngredientDepletionData(call, ingredientDepletionService, timeoutInMillis)
            }
        }
    }

private suspend fun handleGetIngredientDepletionData(
    call: ApplicationCall,
    ingredientDepletionService: IngredientDepletionService,
    timeoutInMillis: Long
) {
    withTimeout(timeoutInMillis) {
        runCatching {
            val brand = call.parameters["brand"]
            val week = call.parameters["week"]
            val dcCodes = call.parameters["dcCodes"]?.split(",")?.map { it.trim() }

            if (brand.isNullOrEmpty() || week.isNullOrEmpty() || dcCodes.isNullOrEmpty()) {
                call.respond(
                    HttpStatusCode.BadRequest,
                    mapToErrorResponse(IllegalArgumentException("Brand, week and dc codes parameters are required")),
                )
                return@withTimeout
            }

            runCatching {
                mapToIngredientDepletionResponse(
                    ingredientDepletionService.getIngredientDepletionData(
                        brand,
                        dcCodes,
                        week
                    ),
                    brand,
                )
            }.onSuccess { result ->
                result.let { call.respond(HttpStatusCode.OK, result) }
            }.onFailure { exception ->
                call.respond(HttpStatusCode.InternalServerError, mapToErrorResponse(exception))
            }
        }.onFailure { exception ->
            call.respond(HttpStatusCode.BadRequest, mapToErrorResponse(exception))
        }
    }
}

fun mapToIngredientDepletionResponse(
    ingredientDepletionDto: IngredientDepletionResponseDto,
    brand: String
): IngredientDepletionResponse =
    IngredientDepletionResponse(
        ingredientSummary = ingredientDepletionDto.ingredientSummary.map { summary ->
            IngredientSummary(
                site = summary.site,
                skuCode = summary.skuCode,
                skuName = summary.skuName,
                category = summary.category,
                uom = summary.uom.toString(),
                brand = brand,
            )
        },
    )

fun ingredientDepletionModule(
    ingredientDepletionService: IngredientDepletionService,
    timeout: Duration,
): Application.() -> Unit = {
    routing {
        ingredientDepletion(
            ingredientDepletionService,
            timeout.inWholeMilliseconds,
        ).also {
            configureSerialization(it)
        }
    }
}
