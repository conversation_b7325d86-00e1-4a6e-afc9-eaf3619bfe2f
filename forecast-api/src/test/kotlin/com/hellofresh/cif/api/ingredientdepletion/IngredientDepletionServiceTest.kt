package com.hellofresh.cif.api.ingredientdepletion

import com.hellofresh.cif.models.SkuUOM
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.BeforeEach

class IngredientDepletionServiceTest {

    private val ingredientDepletionRepository = mockk<IngredientDepletionRepository>()

    private lateinit var service: IngredientDepletionService

    @BeforeEach
    fun setUp() {
        service = IngredientDepletionService(ingredientDepletionRepository)
    }

    @Test
    fun `should use provided dc codes when specified`() = runBlocking {
        // Given
        val brand = "hellofresh"
        val week = "2024-W01"
        val providedDcCodes = listOf("GB01", "DE01")
        val expectedResponse = IngredientDepletionResponseDto(
            ingredientSummary = listOf(
                IngredientSummaryDto(
                    site = "GB01",
                    skuCode = "SKU001",
                    skuName = "Test SKU",
                    category = "Test Category",
                    uom = SkuUOM.UOM_UNIT
                )
            )
        )

        coEvery { ingredientDepletionRepository.getIngredientDepletionData(providedDcCodes, week, brand) } returns expectedResponse

        // When
        val result = service.getIngredientDepletionData(brand, providedDcCodes, week)

        // Then
        assertEquals(expectedResponse, result)
        coVerify { ingredientDepletionRepository.getIngredientDepletionData(providedDcCodes, week, brand) }
    }

    @Test
    fun `should get ingredient depletion data with all required parameters`() = runBlocking {
        // Given
        val brand = "hellofresh"
        val week = "2024-W01"
        val dcCodes = listOf("GB01", "DE01", "US01")
        val expectedResponse = IngredientDepletionResponseDto(
            ingredientSummary = listOf(
                IngredientSummaryDto(
                    site = "GB01",
                    skuCode = "SKU001",
                    skuName = "Test SKU",
                    category = "Test Category",
                    uom = SkuUOM.UOM_UNIT
                )
            )
        )

        coEvery { ingredientDepletionRepository.getIngredientDepletionData(dcCodes, week, brand) } returns expectedResponse

        // When
        val result = service.getIngredientDepletionData(brand, dcCodes, week)

        // Then
        assertEquals(expectedResponse, result)
        coVerify { ingredientDepletionRepository.getIngredientDepletionData(dcCodes, week, brand) }
    }

    @Test
    fun `should get ingredient depletion data for greenchef brand`() = runBlocking {
        // Given
        val brand = "greenchef"
        val week = "2024-W01"
        val dcCodes = listOf("GB01", "US01") // Only DCs that support greenchef
        val expectedResponse = IngredientDepletionResponseDto(
            ingredientSummary = listOf(
                IngredientSummaryDto(
                    site = "GB01",
                    skuCode = "SKU001",
                    skuName = "Green Chef SKU",
                    category = "Green Category",
                    uom = SkuUOM.UOM_KG
                )
            )
        )

        coEvery { ingredientDepletionRepository.getIngredientDepletionData(dcCodes, week, brand) } returns expectedResponse

        // When
        val result = service.getIngredientDepletionData(brand, dcCodes, week)

        // Then
        assertEquals(expectedResponse, result)
        coVerify { ingredientDepletionRepository.getIngredientDepletionData(dcCodes, week, brand) }
    }

    @Test
    fun `should get ingredient depletion data for everyplate brand`() = runBlocking {
        // Given
        val brand = "everyplate"
        val week = "2024-W01"
        val dcCodes = listOf("US01") // Only US DC supports everyplate
        val expectedResponse = IngredientDepletionResponseDto(
            ingredientSummary = listOf(
                IngredientSummaryDto(
                    site = "US01",
                    skuCode = "SKU001",
                    skuName = "EveryPlate SKU",
                    category = "EveryPlate Category",
                    uom = SkuUOM.UOM_LBS
                )
            )
        )

        coEvery { ingredientDepletionRepository.getIngredientDepletionData(dcCodes, week, brand) } returns expectedResponse

        // When
        val result = service.getIngredientDepletionData(brand, dcCodes, week)

        // Then
        assertEquals(expectedResponse, result)
        coVerify { ingredientDepletionRepository.getIngredientDepletionData(dcCodes, week, brand) }
    }

    @Test
    fun `should return empty result when no dc codes provided`() = runBlocking {
        // Given
        val brand = "unsupported-brand"
        val week = "2024-W01"
        val dcCodes = emptyList<String>() // No DCs provided
        val expectedResponse = IngredientDepletionResponseDto(ingredientSummary = emptyList())

        coEvery { ingredientDepletionRepository.getIngredientDepletionData(dcCodes, week, brand) } returns expectedResponse

        // When
        val result = service.getIngredientDepletionData(brand, dcCodes, week)

        // Then
        assertEquals(expectedResponse, result)
        coVerify { ingredientDepletionRepository.getIngredientDepletionData(dcCodes, week, brand) }
    }

    @Test
    fun `should handle empty dc codes list when provided`() = runBlocking {
        // Given
        val brand = "hellofresh"
        val week = "2024-W01"
        val providedDcCodes = emptyList<String>()
        val expectedResponse = IngredientDepletionResponseDto(ingredientSummary = emptyList())

        coEvery { ingredientDepletionRepository.getIngredientDepletionData(providedDcCodes, week, brand) } returns expectedResponse

        // When
        val result = service.getIngredientDepletionData(brand, providedDcCodes, week)

        // Then
        assertEquals(expectedResponse, result)
        coVerify { ingredientDepletionRepository.getIngredientDepletionData(providedDcCodes, week, brand) }
    }

    @Test
    fun `should handle repository exception`() = runBlocking {
        // Given
        val brand = "hellofresh"
        val week = "2024-W01"
        val providedDcCodes = listOf("GB01")
        val exception = RuntimeException("Repository error")

        coEvery { ingredientDepletionRepository.getIngredientDepletionData(providedDcCodes, week, brand) } throws exception

        // When & Then
        try {
            service.getIngredientDepletionData(brand, providedDcCodes, week)
            assert(false) { "Expected exception to be thrown" }
        } catch (e: RuntimeException) {
            assertEquals("Repository error", e.message)
        }

        coVerify { ingredientDepletionRepository.getIngredientDepletionData(providedDcCodes, week, brand) }
    }

    @Test
    fun `should use provided dc codes with brand filtering in repository`() = runBlocking {
        // Given - providing DC codes that will be filtered by brand in repository
        val brand = "everyplate" // Only supported by US01
        val week = "2024-W01"
        val providedDcCodes = listOf("GB01", "DE01") // These don't support everyplate
        val expectedResponse = IngredientDepletionResponseDto(
            ingredientSummary = listOf(
                IngredientSummaryDto(
                    site = "GB01",
                    skuCode = "SKU001",
                    skuName = "Test SKU",
                    category = "Test Category",
                    uom = SkuUOM.UOM_UNIT
                )
            )
        )

        coEvery { ingredientDepletionRepository.getIngredientDepletionData(providedDcCodes, week, brand) } returns expectedResponse

        // When
        val result = service.getIngredientDepletionData(brand, providedDcCodes, week)

        // Then - repository handles brand filtering
        assertEquals(expectedResponse, result)
        coVerify { ingredientDepletionRepository.getIngredientDepletionData(providedDcCodes, week, brand) }
    }

    @Test
    fun `should handle multiple ingredient summaries`() = runBlocking {
        // Given
        val brand = "hellofresh"
        val week = "2024-W01"
        val providedDcCodes = listOf("GB01", "US01")
        val expectedResponse = IngredientDepletionResponseDto(
            ingredientSummary = listOf(
                IngredientSummaryDto(
                    site = "GB01",
                    skuCode = "SKU001",
                    skuName = "Test SKU 1",
                    category = "Category 1",
                    uom = SkuUOM.UOM_UNIT
                ),
                IngredientSummaryDto(
                    site = "US01",
                    skuCode = "SKU002",
                    skuName = "Test SKU 2",
                    category = "Category 2",
                    uom = SkuUOM.UOM_KG
                ),
                IngredientSummaryDto(
                    site = "GB01",
                    skuCode = "SKU003",
                    skuName = "Test SKU 3",
                    category = "Category 3",
                    uom = SkuUOM.UOM_LITRE
                )
            )
        )

        coEvery { ingredientDepletionRepository.getIngredientDepletionData(providedDcCodes, week, brand) } returns expectedResponse

        // When
        val result = service.getIngredientDepletionData(brand, providedDcCodes, week)

        // Then
        assertEquals(expectedResponse, result)
        assertEquals(3, result.ingredientSummary.size)
        coVerify { ingredientDepletionRepository.getIngredientDepletionData(providedDcCodes, week, brand) }
    }
}
