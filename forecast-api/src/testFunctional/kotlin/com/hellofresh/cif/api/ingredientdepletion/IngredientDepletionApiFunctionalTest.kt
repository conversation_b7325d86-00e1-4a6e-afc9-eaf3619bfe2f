package com.hellofresh.cif.api.ingredientdepletion

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.KotlinFeature
import com.fasterxml.jackson.module.kotlin.KotlinModule
import com.fasterxml.jackson.module.kotlin.readValue
import com.hellofresh.cif.api.AuthUtils.addAuthHeader
import com.hellofresh.cif.api.FunctionalTest
import com.hellofresh.cif.api.calculation.generated.model.IngredientDepletionResponse
import com.hellofresh.cif.api.ktor.JwtCredentials
import com.hellofresh.cif.api.ktor.configureJwtAuth
import com.hellofresh.cif.api.schema.enums.Uom
import com.hellofresh.cif.api.schema.tables.records.CalculationRecord
import com.hellofresh.cif.api.schema.tables.records.SkuSpecificationRecord
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import io.ktor.client.request.get
import io.ktor.client.statement.HttpResponse
import io.ktor.client.statement.bodyAsText
import io.ktor.http.HttpStatusCode
import io.ktor.server.testing.testApplication
import java.math.BigDecimal
import java.time.LocalDate
import java.util.UUID
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertTrue
import kotlin.time.Duration
import kotlinx.coroutines.runBlocking

class IngredientDepletionApiFunctionalTest : FunctionalTest() {

    private val testJwtSecret = "testSecret"
    private val jwksURI = "https://test.com"
    private val authorName = "testAuthor"
    private val authorEmail = "<EMAIL>"
    private val timeOutInMillis = Duration.parse("PT5S")

    private val objectMapper = ObjectMapper()
        .registerModule(KotlinModule.Builder().configure(KotlinFeature.NullIsSameAsDefault, true).build())
        .findAndRegisterModules()

    @Test
    fun `should return ingredient depletion data for valid brand with specific DC codes`() {
        // Given
        val brand = "hellofresh"
        val dcCodes = "GB01,DE01"
        val skuId1 = UUID.randomUUID()
        val skuId2 = UUID.randomUUID()

        setupTestData(
            listOf(
                createSkuRecord(skuId1, "SKU001", "Test Chicken Breast", "PRO", Uom.UOM_KG),
                createSkuRecord(skuId2, "SKU002", "Test Tomatoes", "VEG", Uom.UOM_UNIT),
            ),
            listOf(
                createCalculationRecord("GB01", skuId1),
                createCalculationRecord("DE01", skuId2),
            ),
        )

        runBlocking {
            // When
            val response = getIngredientDepletionUrl("/api/v1/ingredient-depletion?brand=$brand&dcCodes=$dcCodes")

            // Then
            assertEquals(HttpStatusCode.OK, response.status)
            val body = response.bodyAsText()
            assertNotNull(body)

            val result = objectMapper.readValue<IngredientDepletionResponse>(body)
            assertEquals(2, result.ingredientSummary.size)

            val gbSku = result.ingredientSummary.find { it.site == "GB01" }
            assertNotNull(gbSku)
            assertEquals("SKU001", gbSku.skuCode)
            assertEquals("Test Chicken Breast", gbSku.skuName)
            assertEquals("PRO", gbSku.category)
            assertEquals("UOM_KG", gbSku.uom)
            assertEquals(brand, gbSku.brand)

            val deSku = result.ingredientSummary.find { it.site == "DE01" }
            assertNotNull(deSku)
            assertEquals("SKU002", deSku.skuCode)
            assertEquals("Test Tomatoes", deSku.skuName)
            assertEquals("VEG", deSku.category)
            assertEquals("UOM_UNIT", deSku.uom)
            assertEquals(brand, deSku.brand)
        }
    }

    @Test
    fun `should return ingredient depletion data for valid brand without DC codes`() {
        // Given
        val brand = "hellofresh"
        val skuId1 = UUID.randomUUID()
        val skuId2 = UUID.randomUUID()

        // Setup DC configurations for hellofresh brand
        createDcConfig("GB01", "GB") { brands = arrayOf("hellofresh", "greenchef") }
        createDcConfig("DE01", "DACH") { brands = arrayOf("hellofresh") }
        createDcConfig("US01", "US") { brands = arrayOf("hellofresh", "greenchef", "everyplate") }

        setupTestData(
            listOf(
                createSkuRecord(skuId1, "SKU001", "Test Ingredient 1", "CAT", Uom.UOM_KG),
                createSkuRecord(skuId2, "SKU002", "Test Ingredient 2", "BAK", Uom.UOM_UNIT),
            ),
            listOf(
                createCalculationRecord("GB01", skuId1),
                createCalculationRecord("DE01", skuId2),
                createCalculationRecord("US01", skuId1),
            ),
        )

        runBlocking {
            // When
            val response = getIngredientDepletionUrl("/api/v1/ingredient-depletion?brand=$brand")

            // Then
            assertEquals(HttpStatusCode.OK, response.status)
            val body = response.bodyAsText()
            assertNotNull(body)

            val result = objectMapper.readValue<IngredientDepletionResponse>(body)
            assertEquals(3, result.ingredientSummary.size)

            // Verify all results have the correct brand
            result.ingredientSummary.forEach { ingredient ->
                assertEquals(brand, ingredient.brand)
                assertTrue(ingredient.site in listOf("GB01", "DE01", "US01"))
            }
        }
    }

    @Test
    fun `should return 400 when brand parameter is missing`() {
        runBlocking {
            // When
            val response = getIngredientDepletionUrl("/api/v1/ingredient-depletion")

            // Then
            assertEquals(HttpStatusCode.BadRequest, response.status)
            val body = response.bodyAsText()
            assertTrue(body.contains("Brand parameter is required"))
        }
    }

    @Test
    fun `should return empty result when no calculations exist for given DC codes`() {
        // Given
        val brand = "hellofresh"
        val dcCodes = "NONEXISTENT"
        val skuId = UUID.randomUUID()

        setupTestData(
            listOf(createSkuRecord(skuId, "SKU001", "Test SKU", "TST", Uom.UOM_UNIT)),
            emptyList(), // No calculations
        )

        runBlocking {
            // When
            val response = getIngredientDepletionUrl("/api/v1/ingredient-depletion?brand=$brand&dcCodes=$dcCodes")

            // Then
            assertEquals(HttpStatusCode.OK, response.status)
            val body = response.bodyAsText()
            assertNotNull(body)

            val result = objectMapper.readValue<IngredientDepletionResponse>(body)
            assertEquals(0, result.ingredientSummary.size)
        }
    }

    @Test
    fun `should return 401 when no authentication provided`() {
        runBlocking {
            // When
            val response = getIngredientDepletionUrlWithoutAuth("/api/v1/ingredient-depletion?brand=hellofresh")

            // Then
            assertEquals(HttpStatusCode.Unauthorized, response.status)
        }
    }

    @Test
    fun `should handle multiple SKUs from same DC correctly`() {
        // Given
        val brand = "greenchef"
        val dcCode = "GB01"
        val skuId1 = UUID.randomUUID()
        val skuId2 = UUID.randomUUID()
        val skuId3 = UUID.randomUUID()

        setupTestData(
            listOf(
                createSkuRecord(skuId1, "SKU001", "Organic Chicken", "PRO", Uom.UOM_KG),
                createSkuRecord(skuId2, "SKU002", "Organic Tomatoes", "VEG", Uom.UOM_UNIT),
                createSkuRecord(skuId3, "SKU003", "Organic Milk", "DAI", Uom.UOM_LITRE),
            ),
            listOf(
                createCalculationRecord(dcCode, skuId1),
                createCalculationRecord(dcCode, skuId2),
                createCalculationRecord(dcCode, skuId3),
            ),
        )

        runBlocking {
            // When
            val response = getIngredientDepletionUrl("/api/v1/ingredient-depletion?brand=$brand&dcCodes=$dcCode")

            // Then
            assertEquals(HttpStatusCode.OK, response.status)
            val body = response.bodyAsText()
            assertNotNull(body)

            val result = objectMapper.readValue<IngredientDepletionResponse>(body)
            assertEquals(3, result.ingredientSummary.size)

            // Verify all results are from the same DC
            result.ingredientSummary.forEach { ingredient ->
                assertEquals(dcCode, ingredient.site)
                assertEquals(brand, ingredient.brand)
            }

            // Verify different categories and UOMs are handled correctly
            val categories = result.ingredientSummary.map { it.category }.toSet()
            assertEquals(setOf("PRO", "VEG", "DAI"), categories)

            val uoms = result.ingredientSummary.map { it.uom }.toSet()
            assertEquals(setOf("UOM_KG", "UOM_UNIT", "UOM_LITRE"), uoms)
        }
    }

    private fun setupTestData(skus: List<SkuSpecificationRecord>, calculations: List<CalculationRecord>) {
        // Insert SKUs
        if (skus.isNotEmpty()) {
            insertSkuRecords(skus)
        }

        // Insert calculations
        if (calculations.isNotEmpty()) {
            dsl.batchInsert(calculations).execute()
        }

        // Refresh DC config service to pick up any new configurations
        dcConfigService.fetchOnDemand()
    }

    private fun createSkuRecord(
        id: UUID,
        code: String,
        name: String,
        category: String,
        uom: Uom
    ): SkuSpecificationRecord = SkuSpecificationRecord().apply {
        this.id = id
        this.code = code
        this.name = name
        this.category = category
        this.uom = uom
        this.acceptableCodeLife = 0
        this.packaging = ""
        this.coolingType = ""
        this.market = "GB"
    }

    private fun createCalculationRecord(dcCode: String, skuId: UUID): CalculationRecord =
        CalculationRecord().apply {
            this.dcCode = dcCode
            this.cskuId = skuId
            this.date = LocalDate.now()
            this.productionWeek = "2024-W01"
            this.openingStock = BigDecimal.ZERO
            this.stockUpdate = BigDecimal.ZERO
            this.closingStock = BigDecimal.TEN
            this.netNeeds = BigDecimal.ZERO
            this.strategy = "TEST_STRATEGY"
        }

    private fun getIngredientDepletionUrl(url: String): HttpResponse {
        lateinit var response: HttpResponse
        testApplication {
            this.application {
                configureJwtAuth(JwtCredentials(testJwtSecret, "", "", "", jwksURI), true)
                ingredientDepletionModule(
                    IngredientDepletionService(
                        IngredientDepletionRepositoryImpl(dsl as MetricsDSLContext),
                    ),
                    timeOutInMillis,
                )()
            }
            response = client.get(url) {
                this.addAuthHeader(authorEmail, authorName, testJwtSecret)
            }
        }
        return response
    }

    private fun getIngredientDepletionUrlWithoutAuth(url: String): HttpResponse {
        lateinit var response: HttpResponse
        testApplication {
            this.application {
                configureJwtAuth(JwtCredentials(testJwtSecret, "", "", "", jwksURI), true)
                ingredientDepletionModule(
                    IngredientDepletionService(
                        IngredientDepletionRepositoryImpl(dsl as MetricsDSLContext),
                    ),
                    timeOutInMillis,
                )()
            }
            response = client.get(url)
        }
        return response
    }

    @Test
    fun `should handle empty brand parameter correctly`() {
        runBlocking {
            val response = getIngredientDepletionUrl("/api/v1/ingredient-depletion?brand=")

            assertEquals(HttpStatusCode.BadRequest, response.status)
            val body = response.bodyAsText()
            assertTrue(body.contains("Brand parameter is required"))
        }
    }

    @Test
    fun `should handle whitespace in DC codes parameter`() {
        // Given
        val brand = "hellofresh"
        val dcCodes = " GB01 , DE01 , US01 " // With extra whitespace
        val skuId = UUID.randomUUID()

        setupTestData(
            listOf(createSkuRecord(skuId, "SKU001", "Test SKU", "TST", Uom.UOM_UNIT)),
            listOf(
                createCalculationRecord("GB01", skuId),
                createCalculationRecord("DE01", skuId),
                createCalculationRecord("US01", skuId),
            ),
        )

        runBlocking {
            // When
            val response = getIngredientDepletionUrl("/api/v1/ingredient-depletion?brand=$brand&dcCodes=$dcCodes")

            // Then
            assertEquals(HttpStatusCode.OK, response.status)
            val body = response.bodyAsText()
            val result = objectMapper.readValue<IngredientDepletionResponse>(body)

            assertEquals(3, result.ingredientSummary.size)
            val sites = result.ingredientSummary.map { it.site }.toSet()
            assertEquals(setOf("GB01", "DE01", "US01"), sites)
        }
    }

    @Test
    fun `should return empty result for brand not supported by any DC`() {
        // Given
        val brand = "unsupported-brand"
        val skuId = UUID.randomUUID()

        // Setup DCs that don't support the brand
        createDcConfig("GB01", "GB") { brands = arrayOf("hellofresh", "greenchef") }
        createDcConfig("DE01", "DACH") { brands = arrayOf("hellofresh") }

        setupTestData(
            listOf(createSkuRecord(skuId, "SKU001", "Test SKU", "TST", Uom.UOM_UNIT)),
            listOf(createCalculationRecord("GB01", skuId)),
        )

        runBlocking {
            // When
            val response = getIngredientDepletionUrl("/api/v1/ingredient-depletion?brand=$brand")

            // Then
            assertEquals(HttpStatusCode.OK, response.status)
            val body = response.bodyAsText()
            val result = objectMapper.readValue<IngredientDepletionResponse>(body)

            assertEquals(0, result.ingredientSummary.size)
        }
    }

    @Test
    fun `should handle calculations without corresponding SKU specifications`() {
        // Given
        val brand = "hellofresh"
        val dcCode = "GB01"
        val existingSkuId = UUID.randomUUID()
        val nonExistentSkuId = UUID.randomUUID()

        setupTestData(
            listOf(createSkuRecord(existingSkuId, "SKU001", "Test SKU", "TST", Uom.UOM_UNIT)),
            listOf(
                createCalculationRecord(dcCode, existingSkuId),
                createCalculationRecord(dcCode, nonExistentSkuId), // This SKU doesn't exist in SKU_SPECIFICATION
            ),
        )

        runBlocking {
            // When
            val response = getIngredientDepletionUrl("/api/v1/ingredient-depletion?brand=$brand&dcCodes=$dcCode")

            // Then
            assertEquals(HttpStatusCode.OK, response.status)
            val body = response.bodyAsText()
            val result = objectMapper.readValue<IngredientDepletionResponse>(body)

            // Should only return the SKU that exists in both tables (due to INNER JOIN)
            assertEquals(1, result.ingredientSummary.size)
            assertEquals("SKU001", result.ingredientSummary[0].skuCode)
        }
    }
}
