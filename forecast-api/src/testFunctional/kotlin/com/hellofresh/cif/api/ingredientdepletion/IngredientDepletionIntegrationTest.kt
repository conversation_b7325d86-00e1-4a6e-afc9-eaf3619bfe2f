package com.hellofresh.cif.api.ingredientdepletion

import com.hellofresh.cif.api.FunctionalTest
import com.hellofresh.cif.api.schema.enums.Uom
import com.hellofresh.cif.api.schema.tables.records.CalculationRecord
import com.hellofresh.cif.api.schema.tables.records.SkuSpecificationRecord
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.models.SkuUOM
import java.math.BigDecimal
import java.time.LocalDate
import java.util.UUID
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertTrue
import kotlinx.coroutines.async
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.BeforeEach

class IngredientDepletionIntegrationTest : FunctionalTest() {

    private lateinit var ingredientDepletionService: IngredientDepletionService
    private lateinit var ingredientDepletionRepository: IngredientDepletionRepository

    @BeforeEach
    fun setUpTest() {
        ingredientDepletionRepository = IngredientDepletionRepositoryImpl(dsl as MetricsDSLContext)
        ingredientDepletionService = IngredientDepletionService(ingredientDepletionRepository)
    }

    @Test
    fun `repository should fetch ingredient depletion data with proper join`() = runBlocking {
        // Given
        val dcCodes = listOf("GB01", "DE01")
        val skuId1 = UUID.randomUUID()
        val skuId2 = UUID.randomUUID()
        val skuId3 = UUID.randomUUID()

        // Insert test data
        insertSkuRecords(
            createSkuRecord(skuId1, "CHICKEN-001", "Organic Chicken Breast", "PRO", Uom.UOM_KG),
            createSkuRecord(skuId2, "TOMATO-002", "Cherry Tomatoes", "VEG", Uom.UOM_UNIT),
            createSkuRecord(skuId3, "MILK-003", "Organic Milk", "DAI", Uom.UOM_LITRE)
        )

        // Insert calculations - only for skuId1 and skuId2
        dsl.batchInsert(
            createCalculationRecord("GB01", skuId1),
            createCalculationRecord("DE01", skuId2)
            // Note: skuId3 has no calculation, so should not appear in results
        ).execute()

        // When
        val result = ingredientDepletionRepository.getIngredientDepletionData(dcCodes, "2024-W01", "hellofresh")

        // Then
        assertNotNull(result)
        assertEquals(2, result.ingredientSummary.size)

        val gbIngredient = result.ingredientSummary.find { it.site == "GB01" }
        assertNotNull(gbIngredient)
        assertEquals("CHICKEN-001", gbIngredient.skuCode)
        assertEquals("Organic Chicken Breast", gbIngredient.skuName)
        assertEquals("PRO", gbIngredient.category)
        assertEquals(SkuUOM.UOM_KG, gbIngredient.uom)

        val deIngredient = result.ingredientSummary.find { it.site == "DE01" }
        assertNotNull(deIngredient)
        assertEquals("TOMATO-002", deIngredient.skuCode)
        assertEquals("Cherry Tomatoes", deIngredient.skuName)
        assertEquals("VEG", deIngredient.category)
        assertEquals(SkuUOM.UOM_UNIT, deIngredient.uom)
    }

    @Test
    fun `service should filter DC codes by brand when no DC codes provided`() = runBlocking {
        // Given
        val brand = "hellofresh"
        val skuId = UUID.randomUUID()

        // Setup DC configurations
        createDcConfig("GB01", "GB") { brands = arrayOf("hellofresh", "greenchef") }
        createDcConfig("DE01", "DACH") { brands = arrayOf("hellofresh") }
        createDcConfig("US01", "US") { brands = arrayOf("hellofresh", "greenchef", "everyplate") }
        createDcConfig("FR01", "FR") { brands = arrayOf("greenchef") } // Should be excluded

        insertSkuRecords(createSkuRecord(skuId, "SKU001", "Test SKU", "TST", Uom.UOM_UNIT))

        // Insert calculations for all DCs
        dsl.batchInsert(
            createCalculationRecord("GB01", skuId),
            createCalculationRecord("DE01", skuId),
            createCalculationRecord("US01", skuId),
            createCalculationRecord("FR01", skuId)
        ).execute()

        // When
        val result = ingredientDepletionService.getIngredientDepletionData(brand, listOf("GB01", "DE01", "US01"), "2024-W01")

        // Then
        assertEquals(3, result.ingredientSummary.size) // Should exclude FR01
        val sites = result.ingredientSummary.map { it.site }.toSet()
        assertEquals(setOf("GB01", "DE01", "US01"), sites)
        assertTrue("FR01" !in sites)
    }

    @Test
    fun `service should use provided DC codes even if they don't support the brand`() = runBlocking {
        // Given
        val brand = "everyplate" // Only supported by US01
        val providedDcCodes = listOf("GB01", "DE01") // These don't support everyplate
        val skuId = UUID.randomUUID()

        // Setup DC configurations
        createDcConfig("GB01", "GB") { brands = arrayOf("hellofresh", "greenchef") }
        createDcConfig("DE01", "DACH") { brands = arrayOf("hellofresh") }
        createDcConfig("US01", "US") { brands = arrayOf("hellofresh", "greenchef", "everyplate") }

        insertSkuRecords(createSkuRecord(skuId, "SKU001", "Test SKU", "TST", Uom.UOM_UNIT))

        dsl.batchInsert(
            createCalculationRecord("GB01", skuId),
            createCalculationRecord("DE01", skuId),
            createCalculationRecord("US01", skuId)
        ).execute()

        // When
        val result = ingredientDepletionService.getIngredientDepletionData(brand, providedDcCodes, "2024-W01")

        // Then
        assertEquals(2, result.ingredientSummary.size) // Should use provided DC codes
        val sites = result.ingredientSummary.map { it.site }.toSet()
        assertEquals(setOf("GB01", "DE01"), sites)
    }

    @Test
    fun `repository should handle empty DC codes list`() = runBlocking {
        // Given
        val emptyDcCodes = emptyList<String>()

        // When
        val result = ingredientDepletionRepository.getIngredientDepletionData(emptyDcCodes, "2024-W01", "hellofresh")

        // Then
        assertEquals(0, result.ingredientSummary.size)
    }

    @Test
    fun `repository should handle large dataset efficiently`() = runBlocking {
        // Given
        val dcCodes = listOf("GB01", "DE01", "US01")
        val numberOfSkus = 100
        val skuIds = (1..numberOfSkus).map { UUID.randomUUID() }

        // Insert many SKUs
        val skuRecords = skuIds.mapIndexed { index, skuId ->
            createSkuRecord(
                skuId,
                "SKU ${index + 1}",
                "Test SKU ${index + 1}",
                "C${index % 5}",
                Uom.values()[index % Uom.values().size]
            )
        }
        insertSkuRecords(skuRecords)

        // Insert calculations for each SKU in each DC
        val calculationRecords = mutableListOf<CalculationRecord>()
        dcCodes.forEach { dcCode ->
            skuIds.forEach { skuId ->
                calculationRecords.add(createCalculationRecord(dcCode, skuId))
            }
        }
        dsl.batchInsert(calculationRecords).execute()

        // When
        val startTime = System.currentTimeMillis()
        val result = ingredientDepletionRepository.getIngredientDepletionData(dcCodes, "2024-W01", "hellofresh")
        val endTime = System.currentTimeMillis()

        // Then
        assertEquals(numberOfSkus * dcCodes.size, result.ingredientSummary.size)
        assertTrue(endTime - startTime < 5000) // Should complete within 5 seconds

        // Verify data integrity
        val skuCodes = result.ingredientSummary.map { it.skuCode }.toSet()
        assertEquals(numberOfSkus, skuCodes.size)

        val sites = result.ingredientSummary.map { it.site }.toSet()
        assertEquals(dcCodes.toSet(), sites)
    }

    @Test
    fun `repository should handle all UOM mappings correctly`() = runBlocking {
        // Given
        val dcCode = "GB01"
        val uomTestCases = mapOf(
            Uom.UOM_UNSPECIFIED to SkuUOM.UOM_UNSPECIFIED,
            Uom.UOM_UNIT to SkuUOM.UOM_UNIT,
            Uom.UOM_KG to SkuUOM.UOM_KG,
            Uom.UOM_LBS to SkuUOM.UOM_LBS,
            Uom.UOM_GAL to SkuUOM.UOM_GAL,
            Uom.UOM_LITRE to SkuUOM.UOM_LITRE,
            Uom.UOM_OZ to SkuUOM.UOM_OZ,
            Uom.UOM_UNRECOGNIZED to SkuUOM.UOM_UNRECOGNIZED
        )

        val skuRecords = uomTestCases.keys.mapIndexed { index, uom ->
            val skuId = UUID.randomUUID()
            dsl.batchInsert(createCalculationRecord(dcCode, skuId)).execute()
            createSkuRecord(skuId, "SKU${index + 1}", "Test SKU ${index + 1}", "TST", uom)
        }
        insertSkuRecords(skuRecords)

        // When
        val result = ingredientDepletionRepository.getIngredientDepletionData(listOf(dcCode), "2024-W01", "hellofresh")

        // Then
        assertEquals(uomTestCases.size, result.ingredientSummary.size)

        uomTestCases.forEach { (dbUom, expectedSkuUom) ->
            val ingredient = result.ingredientSummary.find { summary ->
                // Find the SKU record with this UOM
                val skuRecord = skuRecords.find { it.uom == dbUom }
                summary.skuCode == skuRecord?.code
            }
            assertNotNull(ingredient, "Should find ingredient for UOM $dbUom")
            assertEquals(expectedSkuUom, ingredient.uom, "UOM mapping should be correct for $dbUom")
        }
    }

    @Test
    fun `service should handle concurrent requests correctly`() = runBlocking {
        // Given
        val brand = "hellofresh"
        val dcCodes = listOf("GB01", "DE01")
        val skuId = UUID.randomUUID()

        createDcConfig("GB01", "GB") { brands = arrayOf("hellofresh") }
        createDcConfig("DE01", "DACH") { brands = arrayOf("hellofresh") }

        insertSkuRecords(createSkuRecord(skuId, "SKU001", "Test SKU", "TST", Uom.UOM_UNIT))
        dsl.batchInsert(
            createCalculationRecord("GB01", skuId),
            createCalculationRecord("DE01", skuId)
        ).execute()

        // When - Make multiple concurrent requests
        val results = (1..10).map {
            async {
                ingredientDepletionService.getIngredientDepletionData(brand, dcCodes, "2024-W01")
            }
        }.map { it.await() }

        // Then - All results should be consistent
        results.forEach { result ->
            assertEquals(2, result.ingredientSummary.size)
            val sites = result.ingredientSummary.map { it.site }.toSet()
            assertEquals(setOf("GB01", "DE01"), sites)
        }
    }

    private fun createSkuRecord(
        id: UUID,
        code: String,
        name: String,
        category: String,
        uom: Uom
    ): SkuSpecificationRecord = SkuSpecificationRecord().apply {
        this.id = id
        this.code = code
        this.name = name
        this.category = category
        this.uom = uom
        this.acceptableCodeLife = 0
        this.packaging = ""
        this.coolingType = ""
        this.market = "GB"
    }

    private fun createCalculationRecord(dcCode: String, skuId: UUID): CalculationRecord =
        CalculationRecord().apply {
            this.dcCode = dcCode
            this.cskuId = skuId
            this.date = LocalDate.now()
            this.productionWeek = "2024-W01"
            this.openingStock = BigDecimal.ZERO
            this.stockUpdate = BigDecimal.ZERO
            this.closingStock = BigDecimal.TEN
            this.netNeeds = BigDecimal.ZERO
            this.strategy = "TEST_STRATEGY"
        }
}
